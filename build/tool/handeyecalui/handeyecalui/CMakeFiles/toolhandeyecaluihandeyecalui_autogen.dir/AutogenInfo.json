{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen", "CMAKE_BINARY_DIR": "D:/newFuxios/build", "CMAKE_CURRENT_BINARY_DIR": "D:/newFuxios/build/tool/handeyecalui/handeyecalui", "CMAKE_CURRENT_SOURCE_DIR": "D:/newFuxios/tool/handeyecalui/handeyecalui", "CMAKE_EXECUTABLE": "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["D:/newFuxios/tool/handeyecalui/handeyecalui/CMakeLists.txt", "D:/newFuxios/builder/cmake/executable.cmake", "D:/newFuxios/builder/cmake/common.cmake", "D:/newFuxios/builder/cmake/add_eigen.cmake", "D:/newFuxios/builder/cmake/add_boost.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindBoost.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfigVersion.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/boost_system-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/boost_system-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0/libboost_system-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/boost_atomic-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/boost_atomic-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0/libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/boost_date_time-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/boost_date_time-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0/libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/boost_iostreams-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/boost_iostreams-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0/libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/boost_serialization-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/boost_serialization-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0/libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/libboost_thread-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/boost_chrono-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/boost_chrono-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0/libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/boost_program_options-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/boost_program_options-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/BoostDetectToolset-1.78.0.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0/libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config-version.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0/boost_headers-config.cmake", "D:/newFuxios/builder/cmake/add_opencv.cmake", "C:/opt/opencv/build/OpenCVConfig-version.cmake", "C:/opt/opencv/build/OpenCVConfig.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVConfig.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVModules.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVModules-debug.cmake", "C:/opt/opencv/build/x64/vc16/lib/OpenCVModules-release.cmake", "D:/newFuxios/builder/cmake/add_glog.cmake", "D:/newFuxios/builder/cmake/add_aubo.cmake", "D:/newFuxios/builder/cmake/add_robwork.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfigVersion.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkTargets.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkTargets-debug.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkBuildConfig_debug.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts/Qt5ChartsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts/Qt5ChartsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts/Qt5ChartsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts/Qt5ChartsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake", "D:/newFuxios/builder/cmake/qt_deploy.cmake"], "CMAKE_SOURCE_DIR": "D:/newFuxios", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["D:/newFuxios/tool/handeyecalui/handeyecalui/src/KalmanFilter3D.h", "MU", "UVLADIE3JM/moc_KalmanFilter3D.cpp", null], ["D:/newFuxios/tool/handeyecalui/handeyecalui/src/MainWindow.h", "MU", "UVLADIE3JM/moc_MainWindow.cpp", null], ["D:/newFuxios/tool/handeyecalui/handeyecalui/src/TrackingManager.h", "MU", "UVLADIE3JM/moc_TrackingManager.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/include", "INCLUDE_DIR_Debug": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/include_Release", "MOC_COMPILATION_FILE": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["BIND_FORTRAN_LOWERCASE_UNDERSCORE", "BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "MSVC_AMD64", "NOMINMAX", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SERIALPORT_LIB", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32", "WIN32_LEAN_AND_MEAN", "_CRT_SECURE_NO_DEPRECATE", "_CRT_SECURE_NO_WARNINGS", "_SCL_SECURE_NO_WARNINGS", "_WIN32_WINNT=0x0501"], "MOC_DEFINITIONS_MinSizeRel": ["BIND_FORTRAN_LOWERCASE_UNDERSCORE", "BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "MSVC_AMD64", "NOMINMAX", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SERIALPORT_LIB", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32", "WIN32_LEAN_AND_MEAN", "_CRT_SECURE_NO_DEPRECATE", "_CRT_SECURE_NO_WARNINGS", "_SCL_SECURE_NO_WARNINGS", "_WIN32_WINNT=0x0501"], "MOC_DEFINITIONS_RelWithDebInfo": ["BIND_FORTRAN_LOWERCASE_UNDERSCORE", "BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "MSVC_AMD64", "NOMINMAX", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SERIALPORT_LIB", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32", "WIN32_LEAN_AND_MEAN", "_CRT_SECURE_NO_DEPRECATE", "_CRT_SECURE_NO_WARNINGS", "_SCL_SECURE_NO_WARNINGS", "_WIN32_WINNT=0x0501"], "MOC_DEFINITIONS_Release": ["BIND_FORTRAN_LOWERCASE_UNDERSCORE", "BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "MSVC_AMD64", "NOMINMAX", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SERIALPORT_LIB", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32", "WIN32_LEAN_AND_MEAN", "_CRT_SECURE_NO_DEPRECATE", "_CRT_SECURE_NO_WARNINGS", "_SCL_SECURE_NO_WARNINGS", "_WIN32_WINNT=0x0501"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["D:/newFuxios/tool/handeyecalui/handeyecalui/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/opencv/build/include", "C:/opt/glog/include", "C:/opt/aubo/include", "C:/opt/robwork-21.12/robwork-21.12/ext/eigen3", "C:/opt/robwork-21.12/include/robwork-21.12", "C:/opt/robwork-21.12/robwork-21.12/ext/rwyaobi", "C:/opt/robwork-21.12/robwork-21.12/ext/rwpqp", "C:/opt/robwork-21.12/robwork-21.12/ext/qhull/src", "C:/opt/robwork-21.12/robwork-21.12/ext/csgjs/src", "C:/opt/robwork-21.12/robwork-21.12/ext/zlib", "C:/opt/robwork-21.12/robwork-21.12/ext/fcl/fcl/include", "C:/opt/robwork-21.12/robwork-21.12/ext/assimp/include", "C:/opt/robwork-21.12/robwork-21.12/ext", "D:/newFuxios/hardwaredriver/HikVisionCamera/include", "C:/opt/hikvision/include", "D:/newFuxios/hardwaredriver/AuboArcsDriver/include", "C:/opt/auboarcs/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSerialPort", "C:/opt/rttr/include"], "MOC_INCLUDES_MinSizeRel": ["D:/newFuxios/tool/handeyecalui/handeyecalui/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/opencv/build/include", "C:/opt/glog/include", "C:/opt/aubo/include", "C:/opt/robwork-21.12/robwork-21.12/ext/eigen3", "C:/opt/robwork-21.12/include/robwork-21.12", "C:/opt/robwork-21.12/robwork-21.12/ext/rwyaobi", "C:/opt/robwork-21.12/robwork-21.12/ext/rwpqp", "C:/opt/robwork-21.12/robwork-21.12/ext/qhull/src", "C:/opt/robwork-21.12/robwork-21.12/ext/csgjs/src", "C:/opt/robwork-21.12/robwork-21.12/ext/zlib", "C:/opt/robwork-21.12/robwork-21.12/ext/fcl/fcl/include", "C:/opt/robwork-21.12/robwork-21.12/ext/assimp/include", "C:/opt/robwork-21.12/robwork-21.12/ext", "D:/newFuxios/hardwaredriver/HikVisionCamera/include", "C:/opt/hikvision/include", "D:/newFuxios/hardwaredriver/AuboArcsDriver/include", "C:/opt/auboarcs/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSerialPort", "C:/opt/rttr/include"], "MOC_INCLUDES_RelWithDebInfo": ["D:/newFuxios/tool/handeyecalui/handeyecalui/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/opencv/build/include", "C:/opt/glog/include", "C:/opt/aubo/include", "C:/opt/robwork-21.12/robwork-21.12/ext/eigen3", "C:/opt/robwork-21.12/include/robwork-21.12", "C:/opt/robwork-21.12/robwork-21.12/ext/rwyaobi", "C:/opt/robwork-21.12/robwork-21.12/ext/rwpqp", "C:/opt/robwork-21.12/robwork-21.12/ext/qhull/src", "C:/opt/robwork-21.12/robwork-21.12/ext/csgjs/src", "C:/opt/robwork-21.12/robwork-21.12/ext/zlib", "C:/opt/robwork-21.12/robwork-21.12/ext/fcl/fcl/include", "C:/opt/robwork-21.12/robwork-21.12/ext/assimp/include", "C:/opt/robwork-21.12/robwork-21.12/ext", "D:/newFuxios/hardwaredriver/HikVisionCamera/include", "C:/opt/hikvision/include", "D:/newFuxios/hardwaredriver/AuboArcsDriver/include", "C:/opt/auboarcs/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSerialPort", "C:/opt/rttr/include"], "MOC_INCLUDES_Release": ["D:/newFuxios/tool/handeyecalui/handeyecalui/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/opencv/build/include", "C:/opt/glog/include", "C:/opt/aubo/include", "C:/opt/robwork-21.12/robwork-21.12/ext/eigen3", "C:/opt/robwork-21.12/include/robwork-21.12", "C:/opt/robwork-21.12/robwork-21.12/ext/rwyaobi", "C:/opt/robwork-21.12/robwork-21.12/ext/rwpqp", "C:/opt/robwork-21.12/robwork-21.12/ext/qhull/src", "C:/opt/robwork-21.12/robwork-21.12/ext/csgjs/src", "C:/opt/robwork-21.12/robwork-21.12/ext/zlib", "C:/opt/robwork-21.12/robwork-21.12/ext/fcl/fcl/include", "C:/opt/robwork-21.12/robwork-21.12/ext/assimp/include", "C:/opt/robwork-21.12/robwork-21.12/ext", "D:/newFuxios/hardwaredriver/HikVisionCamera/include", "C:/opt/hikvision/include", "D:/newFuxios/hardwaredriver/AuboArcsDriver/include", "C:/opt/auboarcs/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSerialPort", "C:/opt/rttr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 16, "PARSE_CACHE_FILE": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/CMakeFiles/toolhandeyecaluihandeyecalui_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["D:/newFuxios/tool/handeyecalui/handeyecalui/src/KalmanFilter3D.cpp", "MU", null], ["D:/newFuxios/tool/handeyecalui/handeyecalui/src/MainWindow.cpp", "MU", null], ["D:/newFuxios/tool/handeyecalui/handeyecalui/src/TrackingManager.cpp", "MU", null], ["D:/newFuxios/tool/handeyecalui/handeyecalui/src/main.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}