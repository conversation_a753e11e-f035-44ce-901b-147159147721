{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen", "CMAKE_BINARY_DIR": "D:/newFuxios/build", "CMAKE_CURRENT_BINARY_DIR": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver", "CMAKE_CURRENT_SOURCE_DIR": "D:/newFuxios/RoboticLaserMarking/Test/rfiddriver", "CMAKE_EXECUTABLE": "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["D:/newFuxios/RoboticLaserMarking/Test/rfiddriver/CMakeLists.txt", "D:/newFuxios/builder/cmake/executable.cmake", "D:/newFuxios/builder/cmake/common.cmake", "D:/newFuxios/builder/cmake/add_eigen.cmake", "D:/newFuxios/builder/cmake/add_glog.cmake", "D:/newFuxios/builder/cmake/add_libmodbus.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts/Qt5ChartsConfigVersion.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts/Qt5ChartsConfig.cmake", "D:/newFuxios/builder/cmake/qt_deploy.cmake"], "CMAKE_SOURCE_DIR": "D:/newFuxios", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/include", "INCLUDE_DIR_Debug": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/include_Release", "MOC_COMPILATION_FILE": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/RoboticLaserMarkingTestrfiddriver_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32"], "MOC_DEFINITIONS_MinSizeRel": ["BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32"], "MOC_DEFINITIONS_RelWithDebInfo": ["BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32"], "MOC_DEFINITIONS_Release": ["BOOST_ATOMIC_NO_LIB", "BOOST_CHRONO_NO_LIB", "BOOST_DATE_TIME_NO_LIB", "BOOST_FILESYSTEM_NO_LIB", "BOOST_IOSTREAMS_NO_LIB", "BOOST_PROGRAM_OPTIONS_NO_LIB", "BOOST_SERIALIZATION_NO_LIB", "BOOST_SYSTEM_NO_LIB", "BOOST_THREAD_NO_LIB", "QT_CHARTS_LIB", "QT_CORE_LIB", "QT_DISABLE_DEPRECATED_BEFORE=0", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_WIDGETS_LIB", "RTTR_DLL", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["D:/newFuxios/RoboticLaserMarking/Test/rfiddriver/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/glog/include", "C:/opt/libmodbus/include", "D:/newFuxios/RoboticLaserMarking/RFIDDriver/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/opt/rttr/include"], "MOC_INCLUDES_MinSizeRel": ["D:/newFuxios/RoboticLaserMarking/Test/rfiddriver/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/glog/include", "C:/opt/libmodbus/include", "D:/newFuxios/RoboticLaserMarking/RFIDDriver/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/opt/rttr/include"], "MOC_INCLUDES_RelWithDebInfo": ["D:/newFuxios/RoboticLaserMarking/Test/rfiddriver/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/glog/include", "C:/opt/libmodbus/include", "D:/newFuxios/RoboticLaserMarking/RFIDDriver/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/opt/rttr/include"], "MOC_INCLUDES_Release": ["D:/newFuxios/RoboticLaserMarking/Test/rfiddriver/include", "C:/opt/PCL/3rdParty/Eigen/eigen3", "C:/opt/glog/include", "C:/opt/libmodbus/include", "D:/newFuxios/RoboticLaserMarking/RFIDDriver/include", "D:/newFuxios/fuxicommon/include", "C:/opt/openssl/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts", "C:/opt/PCL/3rdParty/Boost/include/boost-1_78", "C:/opt/rttr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 16, "PARSE_CACHE_FILE": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "D:/newFuxios/build/RoboticLaserMarking/Test/rfiddriver/CMakeFiles/RoboticLaserMarkingTestrfiddriver_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["D:/newFuxios/RoboticLaserMarking/Test/rfiddriver/src/test_rfid_driver.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}